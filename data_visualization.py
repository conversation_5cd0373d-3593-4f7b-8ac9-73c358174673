import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.dates as mdates
from matplotlib.colors import LinearSegmentedColormap
from matplotlib import cm
import matplotlib.ticker as ticker
from datetime import datetime
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建输出目录
output_dir = 'output_visualizations'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 设置更深的蓝色和黑色为主色调
deep_blue_colors = ['#0d47a1', '#1565c0', '#1976d2', '#1e88e5', '#2196f3', '#42a5f5', '#64b5f6', '#90caf9', '#bbdefb']
deep_colors = ['#1a237e', '#283593', '#303f9f', '#3949ab', '#3f51b5', '#5c6bc0', '#7986cb', '#9fa8da', '#c5cae9']
black_colors = ['#000000', '#212121', '#424242', '#616161', '#757575', '#9e9e9e', '#bdbdbd', '#e0e0e0', '#f5f5f5']

# 自定义深色调色板
deep_blue_cmap = LinearSegmentedColormap.from_list('DeepBlues', deep_blue_colors[::-1])  # 反转顺序，深色在前
deep_cmap = LinearSegmentedColormap.from_list('DeepColors', deep_colors[::-1])  # 反转顺序，深色在前
blue_black_cmap = LinearSegmentedColormap.from_list('BlueBlack', ['#0d47a1', '#000000'])

# 打印调试信息
def print_debug(message):
    print(f"DEBUG: {message}")

# 1. 加载数据集1: 某公司产品销售数据
def load_sales_data():
    try:
        sales_data = pd.read_excel('数据可视化数据集-A/某公司产品销售数据.xlsx')
        print("成功加载某公司产品销售数据")
        print(sales_data.head())
        print(sales_data.info())
        return sales_data
    except Exception as e:
        print(f"加载某公司产品销售数据时出错: {e}")
        return None

# 2. 加载数据集2: 某餐厅顾客消费记录
def load_restaurant_data():
    try:
        restaurant_data = pd.read_excel('数据可视化数据集-A/某餐厅顾客消费记录.xlsx')
        print("成功加载某餐厅顾客消费记录")
        print(restaurant_data.head())
        print(restaurant_data.info())
        return restaurant_data
    except Exception as e:
        print(f"加载某餐厅顾客消费记录时出错: {e}")
        return None

# 3. 加载数据集3: 营销和产品销售表
def load_marketing_data():
    try:
        marketing_data = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')
        print("成功加载营销和产品销售表")
        print(marketing_data.head())
        print(marketing_data.info())
        return marketing_data
    except Exception as e:
        print(f"加载营销和产品销售表时出错: {e}")
        return None

# 数据集1的可视化函数 - 某公司产品销售数据
def visualize_sales_data(sales_data):
    if sales_data is None:
        return

    print_debug("开始生成某公司产品销售数据可视化")

    # 图1: 折线图 - 各地区销售趋势
    plt.figure(figsize=(12, 6))

    # 检查数据集中的列
    print_debug(f"销售数据列: {sales_data.columns.tolist()}")

    # 使用季度和地区作为分组依据，销售额作为值
    if '季度' in sales_data.columns and '地区' in sales_data.columns and '销售额（万元）' in sales_data.columns:
        # 按地区和季度分组，计算销售总额
        colors = ['#0d47a1', '#1a237e', '#b71c1c']  # 深蓝、深紫、深红
        for i, (region, group) in enumerate(sales_data.groupby('地区')):
            plt.plot(group['季度'], group['销售额（万元）'], marker='o', linewidth=2, label=region, color=colors[i])

        plt.title('各地区销售趋势', fontsize=16)
        plt.xlabel('季度', fontsize=12)
        plt.ylabel('销售额（万元）', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend(title='地区', fontsize=10)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/region_sales_trend_line_chart.png', dpi=300)
        plt.close()
        print_debug("已生成折线图: region_sales_trend_line_chart.png")
    else:
        print_debug("无法生成折线图，缺少必要的列")

    # 图2: 簇状柱形图 - 各地区销售对比
    plt.figure(figsize=(12, 6))

    if '地区' in sales_data.columns and '销售额（万元）' in sales_data.columns:
        # 按地区分组，计算销售总额
        region_sales = sales_data.groupby('地区')['销售额（万元）'].sum().reset_index()

        # 绘制柱状图
        sns.barplot(x='地区', y='销售额（万元）', data=region_sales, palette=deep_blue_colors[:3])

        plt.title('各地区销售总额对比', fontsize=16)
        plt.xlabel('地区', fontsize=12)
        plt.ylabel('销售总额（万元）', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/region_sales_bar_chart.png', dpi=300)
        plt.close()
        print_debug("已生成柱状图: region_sales_bar_chart.png")
    else:
        print_debug("无法生成柱状图，缺少必要的列")

    # 图3: 饼图 - 各地区销售占比
    plt.figure(figsize=(10, 8))

    if '地区' in sales_data.columns and '销售额（万元）' in sales_data.columns:
        # 按地区分组，计算销售总额
        region_sales = sales_data.groupby('地区')['销售额（万元）'].sum()

        # 绘制饼图
        plt.pie(region_sales, labels=region_sales.index, autopct='%1.1f%%',
                startangle=90, shadow=True, colors=deep_blue_colors[:3])

        plt.title('各地区销售占比', fontsize=16)
        plt.axis('equal')  # 确保饼图是圆的
        plt.tight_layout()
        plt.savefig(f'{output_dir}/region_sales_pie_chart.png', dpi=300)
        plt.close()
        print_debug("已生成饼图: region_sales_pie_chart.png")
    else:
        print_debug("无法生成饼图，缺少必要的列")

# 数据集2的可视化函数 - 某餐厅顾客消费记录
def visualize_restaurant_data(restaurant_data):
    if restaurant_data is None:
        return

    print_debug("开始生成某餐厅顾客消费记录可视化")
    print_debug(f"餐厅数据列: {restaurant_data.columns.tolist()}")

    # 图1: 箱线图 - 不同分店消费金额分布
    plt.figure(figsize=(12, 6))

    if '分店' in restaurant_data.columns and '消费金额（元）' in restaurant_data.columns:
        # 绘制箱线图
        sns.boxplot(x='分店', y='消费金额（元）', data=restaurant_data, palette=deep_colors[:3])

        plt.title('不同分店消费金额分布', fontsize=16)
        plt.xlabel('分店', fontsize=12)
        plt.ylabel('消费金额（元）', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/restaurant_branch_boxplot.png', dpi=300)
        plt.close()
        print_debug("已生成箱线图: restaurant_branch_boxplot.png")
    else:
        print_debug("无法生成箱线图，缺少必要的列")

    # 图2: 小提琴图 - 不同顾客类型消费金额分布
    plt.figure(figsize=(12, 6))

    if '顾客类型' in restaurant_data.columns and '消费金额（元）' in restaurant_data.columns:
        # 绘制小提琴图
        sns.violinplot(x='顾客类型', y='消费金额（元）', data=restaurant_data, palette=deep_colors[:2])

        plt.title('不同顾客类型消费金额分布', fontsize=16)
        plt.xlabel('顾客类型', fontsize=12)
        plt.ylabel('消费金额（元）', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/customer_type_violin_plot.png', dpi=300)
        plt.close()
        print_debug("已生成小提琴图: customer_type_violin_plot.png")
    else:
        print_debug("无法生成小提琴图，缺少必要的列")

    # 图3: 条形图 - 不同性别顾客满意度对比
    plt.figure(figsize=(12, 6))

    if '性别' in restaurant_data.columns and '顾客满意度' in restaurant_data.columns:
        # 按性别分组，计算平均满意度
        gender_satisfaction = restaurant_data.groupby('性别')['顾客满意度'].mean().reset_index()

        # 绘制条形图
        sns.barplot(x='性别', y='顾客满意度', data=gender_satisfaction, palette=deep_colors[:2])

        plt.title('不同性别顾客满意度对比', fontsize=16)
        plt.xlabel('性别', fontsize=12)
        plt.ylabel('平均满意度', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/gender_satisfaction_bar_chart.png', dpi=300)
        plt.close()
        print_debug("已生成条形图: gender_satisfaction_bar_chart.png")
    else:
        print_debug("无法生成条形图，缺少必要的列")

# 数据集3的可视化函数 - 营销和产品销售表
def visualize_marketing_data(marketing_data):
    if marketing_data is None:
        return

    print_debug("开始生成营销和产品销售表可视化")
    print_debug(f"营销数据列: {marketing_data.columns.tolist()}")

    # 图1: 散点图 - 营销费用与订单金额的关系
    plt.figure(figsize=(10, 8))

    if '营销费用（元）' in marketing_data.columns and '订单金额（元）' in marketing_data.columns:
        # 绘制散点图
        plt.scatter(marketing_data['营销费用（元）'], marketing_data['订单金额（元）'],
                   alpha=0.8, s=100, c=marketing_data['营销费用（元）'], cmap=deep_blue_cmap)

        # 添加趋势线
        z = np.polyfit(marketing_data['营销费用（元）'], marketing_data['订单金额（元）'], 1)
        p = np.poly1d(z)
        plt.plot(marketing_data['营销费用（元）'], p(marketing_data['营销费用（元）']),
                "r--", linewidth=2, color='#000000')

        plt.title('营销费用与订单金额的关系', fontsize=16)
        plt.xlabel('营销费用（元）', fontsize=12)
        plt.ylabel('订单金额（元）', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.colorbar(label='营销费用（元）')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/marketing_order_scatter.png', dpi=300)
        plt.close()
        print_debug("已生成散点图: marketing_order_scatter.png")
    else:
        print_debug("无法生成散点图，缺少必要的列")

    # 图2: 折线图 - 营销费用与点击量的时间趋势
    plt.figure(figsize=(12, 6))

    if '日期' in marketing_data.columns and '营销费用（元）' in marketing_data.columns and '点击量' in marketing_data.columns:
        # 确保日期列是日期时间类型
        marketing_data['日期'] = pd.to_datetime(marketing_data['日期'])

        # 创建双Y轴图表
        fig, ax1 = plt.subplots(figsize=(12, 6))

        # 第一个Y轴 - 营销费用
        color = '#2171b5'  # 使用蓝色的十六进制代码
        ax1.set_xlabel('日期', fontsize=12)
        ax1.set_ylabel('营销费用（元）', color=color, fontsize=12)
        ax1.plot(marketing_data['日期'], marketing_data['营销费用（元）'], color=color, marker='o')
        ax1.tick_params(axis='y', labelcolor=color)

        # 第二个Y轴 - 点击量
        ax2 = ax1.twinx()
        color = '#000000'  # 使用黑色的十六进制代码
        ax2.set_ylabel('点击量', color=color, fontsize=12)
        ax2.plot(marketing_data['日期'], marketing_data['点击量'], color=color, marker='s')
        ax2.tick_params(axis='y', labelcolor=color)

        plt.title('营销费用与点击量的时间趋势', fontsize=16)
        plt.grid(True, linestyle='--', alpha=0.7)
        fig.tight_layout()
        plt.savefig(f'{output_dir}/marketing_clicks_trend.png', dpi=300)
        plt.close()
        print_debug("已生成折线图: marketing_clicks_trend.png")
    else:
        print_debug("无法生成折线图，缺少必要的列")

    # 图3: 热力图 - 营销指标相关性
    plt.figure(figsize=(10, 8))

    # 选择数值型列进行相关性分析
    numeric_cols = marketing_data.select_dtypes(include=['float64', 'int64']).columns.tolist()

    if len(numeric_cols) >= 2:
        # 计算相关性矩阵
        corr_matrix = marketing_data[numeric_cols].corr()

        # 绘制热力图
        sns.heatmap(corr_matrix, annot=True, cmap=deep_blue_cmap, fmt='.2f', linewidths=.5)

        plt.title('营销指标相关性热力图', fontsize=16)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/marketing_correlation_heatmap.png', dpi=300)
        plt.close()
        print_debug("已生成热力图: marketing_correlation_heatmap.png")
    else:
        print_debug("无法生成热力图，数值型列不足")

def main():
    # 加载数据
    sales_data = load_sales_data()
    restaurant_data = load_restaurant_data()
    marketing_data = load_marketing_data()

    # 生成可视化
    visualize_sales_data(sales_data)
    visualize_restaurant_data(restaurant_data)
    visualize_marketing_data(marketing_data)

    print(f"所有可视化图表已保存到 {output_dir} 目录")

if __name__ == "__main__":
    main()
